<template>
  <a-drawer
    v-model:open="visible"
    class="edit-lease-unit-drawer common-drawer"
    :title="`${formData.id ? '编辑' : '添加'}租赁单元`"
    placement="right"
    width="1072px"
    :mask-closable="false"
  >
    <a-spin :spinning="confirmLoading">
      <circle-steps
        :current="currentStep + 1"
        :step-list="stepList"
        width="1072px"
        class="mx-auto mb-[40px]"
      ></circle-steps>
      <basic-info-step
        v-show="currentStep === 0"
        ref="basicFormRef"
        :form-data="formData"
        :rules="rules"
        @house-owner-change="handleHouseOwnerChange"
        @project-change="handleProjectChange"
      />

      <lease-info-step v-show="currentStep === 1" ref="leaseFormRef" :form-data="formData" :rules="rules" />

      <building-info-step v-show="currentStep === 2" ref="landFormRef" :form-data="formData" :rules="rules" />

      <water-electric-step v-show="currentStep === 3" :form-data="formData" />

      <attachment-step v-show="currentStep === 4" :form-data="formData" />
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :disabled="currentStep <= 0" @click="handlePrevStep">上一步</a-button>
      <a-button :disabled="currentStep >= stepList.length - 1" @click="handleNextStep">下一步</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addLeaseUnit, submitLeaseUnit, editLeaseUnit } from '../apis/leaseUnit'
import BasicInfoStep from './BasicInfoStep.vue'
import LeaseInfoStep from './LeaseInfoStep.vue'
import BuildingInfoStep from './BuildingInfoStep.vue'
import WaterElectricStep from './WaterElectricStep.vue'
import AttachmentStep from './AttachmentStep.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const currentStep = ref(0)
const confirmLoading = ref(false)

const basicFormRef = ref()
const leaseFormRef = ref()
const landFormRef = ref()

const stepList = ['基础信息', '租赁信息', '土地及建筑物信息', '水电费用', '附件信息']

/**
 * 验证省市区代码数组
 * @param {any} _rule - 验证规则
 * @param {Array} value - 省市区代码数组
 * @returns {Promise} 验证结果
 */
const validatePcaCodeArray = (_rule, value) => {
  if (!value) return Promise.reject('请选择省市区')
  if (!Array.isArray(value) || value.length === 0) return Promise.reject('请选择完整的省市区信息')
  if (value.length < 3) return Promise.reject('请选择到区级行政区')
  return Promise.resolve()
}

/**
 * 验证详细地址
 * @param {any} _rule - 验证规则
 * @param {string} value - 详细地址
 * @returns {Promise} 验证结果
 */
const validateDetailAddress = (_rule, value) => {
  if (!value || value.trim() === '') return Promise.reject('请输入详细地址')
  return Promise.resolve()
}

const defaultFormData = reactive({
  id: undefined,
  name: undefined,
  virtualLeaseUnit: false,
  houseOwner: undefined,
  houseOwner_dictText: undefined,
  propertyUse: undefined,
  wyProject: undefined,
  wyBuilding: undefined,
  wyFloor: undefined,
  wyProjectArray: [],
  province: undefined,
  city: undefined,
  area: undefined,
  pcaCode: undefined,
  pcaCodeArray: [],
  detailAddress: undefined,
  assetType: undefined,
  ownerCompany: undefined,
  collectionCompany: undefined,
  manageCompany: undefined,
  landNature: undefined,
  treeId: undefined,
  supportFacility: undefined,
  remark: undefined,
  useType: undefined,
  leaseArea: undefined,
  leaseUse: undefined,
  areaManager: undefined,
  effectDate: undefined,
  expireDate: undefined,
  houseType: undefined,
  structureArea: undefined,
  floorArea: undefined,
  buildStructrue: undefined,
  buildYear: undefined,
  layerNum: undefined,
  layerHight: undefined,
  houseModel: undefined,
  firefightingRate: undefined,
  houseSafeRate: undefined,
  houseTaxOrgValue: undefined,
  addTaxRate: undefined,
  invoiceAddress: undefined,
  waterElectricList: [],
  attachmentIds: undefined,
  files: [],
  currentLayer: undefined,
  totalLayer: undefined
})

const formData = reactive({ ...defaultFormData })

const rules = {
  name: [
    { required: true, message: '请输入单元名称', trigger: 'blur' },
    { min: 2, max: 50, message: '单元名称长度应为2-50个字符', trigger: 'blur' }
  ],
  pcaCodeArray: [{ required: true, validator: validatePcaCodeArray, trigger: 'change' }],
  detailAddress: [{ required: true, validator: validateDetailAddress, trigger: ['blur', 'change'] }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  collectionCompany: [{ required: true, message: '请选择租金归集公司', trigger: 'change' }],
  ownerCompany: [{ required: true, message: '请选择资产权属公司', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  landNature: [{ required: true, message: '请选择使用权类型', trigger: 'change' }],
  treeId: [{ required: true, message: '请选择租赁单元分类', trigger: 'change' }],
  supportFacility: [{ max: 500, message: '配套设施描述不能超过500个字符', trigger: 'blur' }],
  remark: [{ max: 500, message: '备注不能超过500个字符', trigger: 'blur' }],
  useType: [{ required: true, message: '请选择使用类型', trigger: 'change' }],
  leaseArea: [
    { required: true, message: '请输入租赁面积', trigger: 'blur' },
    { type: 'number', min: 0, message: '租赁面积不能为负数', trigger: 'blur' }
  ],
  leaseUse: [{ required: true, message: '请选择租赁用途', trigger: 'change' }],
  effectDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
  expireDate: [
    { required: true, message: '请选择到期日期', trigger: 'change' },
    {
      validator: (_rule, value) => {
        if (!value || !formData.effectDate) return Promise.resolve()
        return value >= formData.effectDate ? Promise.resolve() : Promise.reject('到期日期不能早于生效日期')
      },
      trigger: 'change'
    }
  ],
  structureArea: [
    { required: true, message: '请输入建筑面积', trigger: 'blur' },
    { type: 'number', min: 0, message: '建筑面积不能为负数', trigger: 'blur' }
  ],
  floorArea: [{ type: 'number', min: 0, message: '宗地面积不能为负数', trigger: 'blur' }],
  buildStructure: [{ required: true, message: '请选择建筑结构', trigger: 'change' }],
  currentLayer: [{ pattern: /^\d*$/, message: '层数必须为非负整数', trigger: 'blur' }],
  totalLayer: [{ pattern: /^\d*$/, message: '总层数必须为非负整数', trigger: 'blur' }],
  layerHight: [{ type: 'number', min: 0, message: '层高不能为负数', trigger: 'blur' }],
  houseSafeRate: [{ required: true, message: '请选择房屋安全等级', trigger: 'change' }],
  houseTaxOrgValue: [{ type: 'number', min: 0, message: '房产税计税原值不能为负数', trigger: 'blur' }],
  addTaxRate: [{ type: 'number', min: 0, message: '开票增值税率不能为负数', trigger: 'blur' }]
}

/**
 * 打开编辑弹窗
 * @param {Object} data - 租赁单元数据，包含id等字段
 */
const open = (data) => {
  visible.value = true

  if (data?.id) {
    Object.assign(formData, data)
    formData.propertyUse = formData.propertyUse || undefined

    // 处理资产数据
    if (formData.virtualLeaseUnit !== true) {
      formData.houseOwner = undefined
      formData.houseOwner_dictText = undefined
    }

    // 处理层数数据
    if (formData.layerNum) {
      const [currentLayer = '', totalLayer = ''] = formData.layerNum.split('/')
      Object.assign(formData, { currentLayer, totalLayer })
    }

    if (formData.buildYear) {
      formData.buildYear = String(formData.buildYear)
    }

    // 处理省市区数据
    formData.pcaCodeArray = formData.pcaCode?.split(',').filter(Boolean) || []

    // 处理水电表数据
    formData.waterElectricList = Array.isArray(formData.waterElectricList) ? formData.waterElectricList : []

    // 处理项目楼栋楼层数据
    if (formData.wyProject || formData.wyBuilding || formData.wyFloor) {
      // 延迟调用子组件的初始化项目数据方法
      nextTick(() => {
        basicFormRef.value?.initProjectData()
      })
    }
  } else {
    Object.assign(formData, defaultFormData)
    formData.waterElectricList = []
    formData.areaManager = undefined
  }
}

/**
 * 处理资产选择变更事件
 * @param {Object} selectItem - 选中的资产对象，包含关联的项目、省市区等信息
 */
const handleHouseOwnerChange = (selectItem) => {
  if (!selectItem) {
    formData.houseOwner = undefined
    return
  }
  formData.houseOwner = selectItem.id
  // 关联项目-楼栋-楼层
  formData.wyProject = selectItem.wyProject
  formData.wyBuilding = selectItem.wyBuilding
  formData.wyFloor = selectItem.wyFloor
  formData.wyProjectArray = [formData.wyProject, formData.wyBuilding, formData.wyFloor].filter(Boolean)
  // 关联省市区
  formData.province = selectItem.province
  formData.city = selectItem.city
  formData.area = selectItem.area
  formData.pcaCodeArray = [formData.province, formData.city, formData.area].filter(Boolean)
  // 关联详细地址
  formData.detailAddress = selectItem.detailAddress
}

/**
 * 处理项目楼栋楼层选择变更事件
 * @param {Array} value - 选中的项目楼栋楼层ID数组
 */
const handleProjectChange = (value) => {
  formData.wyProjectArray = value
  ;[formData.wyProject, formData.wyBuilding, formData.wyFloor] = value
}

/**
 * 执行下一步操作，验证当前步骤表单后进入下一步
 */
const handleNextStep = async () => {
  if (currentStep.value >= stepList.length - 1) return

  try {
    switch (currentStep.value) {
      case 0:
        await basicFormRef.value?.validate()
        break
      case 1:
        await leaseFormRef.value?.validate()
        break
      case 2:
        await landFormRef.value?.validate()
        break
    }
    currentStep.value++
  } catch {
    message.error('请填写完必填项后再进入下一步')
  }
}

/**
 * 执行上一步操作，返回到上一个步骤
 */
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  try {
    // 验证所有表单
    await Promise.all([basicFormRef.value?.validate(), leaseFormRef.value?.validate(), landFormRef.value?.validate()])
  } catch {
    message.error('请检查并填写完所有必填项')
    confirmLoading.value = false
    return
  }
  // 根据操作类型选择对应的API
  const api = formData.id ? editLeaseUnit : isTemporary ? addLeaseUnit : submitLeaseUnit

  try {
    formData.pcaCode = formData.pcaCodeArray.join(',')

    const result = await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '添加'
    message.success(`租赁单元${action}成功`)

    emits('refresh', result?.result || formData)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 保存并提交租赁单元数据
 */
const handleSave = () => saveData(false)

/**
 * 暂存租赁单元数据
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 取消操作并重置表单数据
 */
const handleCancel = () => {
  visible.value = false
  currentStep.value = 0
  Object.assign(formData, defaultFormData)
  formData.files = []
  basicFormRef.value?.resetFields()
  leaseFormRef.value?.resetFields()
  landFormRef.value?.resetFields()
}

defineExpose({ open })
</script>
<style lang="less">
.edit-lease-unit-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }

  .site-input-split {
    background-color: #fff;
  }
  .site-input-right {
    border-left-width: 0;
    &:hover,
    &:focus {
      border-left-width: 1px;
    }
  }

  .ant-input-rtl.site-input-right {
    border-right-width: 0;
    &:hover,
    &:focus {
      border-right-width: 1px;
    }
  }
}
</style>
