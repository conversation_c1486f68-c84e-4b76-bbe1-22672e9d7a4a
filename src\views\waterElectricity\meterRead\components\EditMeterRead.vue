<template>
  <a-modal
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新增'}抄表数`"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="水电表" name="waterElectriCityTableId" required>
          <water-electricity-select v-model="formData.waterElectriCityTableId" />
        </a-form-item>
        <a-form-item label="上月表数" name="tableNumber" required>
          <a-input-number
            v-model:value="formData.tableNumber"
            placeholder="请输入上月表数"
            style="width: 100%"
            :min="0"
            :precision="2"
          />
        </a-form-item>
        <a-form-item label="归属年月" name="meterReadDate" required>
          <a-date-picker
            v-model:value="formData.meterReadDate"
            placeholder="请选择归属年月"
            style="width: 100%"
            value-format="YYYY-MM"
            picker="month"
          />
        </a-form-item>
        <a-form-item label="本月抄表时间" name="meterReadBelongDate" required>
          <a-date-picker
            v-model:value="formData.meterReadBelongDate"
            placeholder="请选择本月抄表时间"
            style="width: 100%"
            value-format="YYYY-MM-DD"
          />
        </a-form-item>
        <a-form-item label="本月表数" name="tableNumber" required>
          <a-input-number
            v-model:value="formData.tableNumber"
            placeholder="请输入本月表数"
            style="width: 100%"
            :min="0"
            :precision="2"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import WaterElectricitySelect from './WaterElectricitySelect.vue'
import { queryWaterElectricityById } from '@/views/waterElectricity/manage/apis/waterElectricity'
import { addMeterRead, editMeterRead } from '../apis'

const emit = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

// 表单默认值
const formDataDefault = reactive({
  id: undefined,
  waterElectriCityTableId: undefined,
  waterElectriCityTableNum: undefined,
  meterReadBelongDate: undefined,
  tableNumber: undefined
})

const formData = reactive({ ...formDataDefault })

// 表单验证规则
const rules = {
  waterElectriCityTableId: [{ required: true, message: '请选择水电表', trigger: 'submit' }],
  meterReadBelongDate: [{ required: true, message: '请选择抄表时间', trigger: 'change' }],
  tableNumber: [
    { required: true, message: '请输入表数', trigger: 'blur' },
    { type: 'number', min: 0, message: '表数不能为负数', trigger: 'blur' }
  ]
}

/**
 * 保存抄表数信息
 */
const handleSave = async () => {
  await formRef.value.validate()
  confirmLoading.value = true
  try {
    const { result } = await queryWaterElectricityById({ id: formData.waterElectriCityTableId })
    formData.waterElectriCityTableNum = result.number
    if (formData.id) {
      await editMeterRead(formData)
      message.success('编辑成功')
    } else {
      await addMeterRead(formData)
      message.success('添加成功')
    }
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 取消操作
 */
const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, { ...formDataDefault })
  visible.value = false
  emit('refresh')
}

/**
 * 打开弹窗
 * @param {Object} record 抄表数记录
 */
const open = (record) => {
  visible.value = true
  confirmLoading.value = true

  try {
    if (record?.id) {
      Object.assign(formData, record)
    }
  } finally {
    confirmLoading.value = false
  }
}

defineExpose({ open })
</script>
