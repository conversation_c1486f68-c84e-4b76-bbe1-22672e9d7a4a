<template>
  <a-drawer
    v-model:open="visible"
    class="lease-unit-split-merge-edit-drawer common-drawer"
    :title="`${formData.id ? '编辑' : '添加'}租赁单元拆合单`"
    placement="right"
    width="1072px"
    :mask-closable="false"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">基础信息</h4>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="物业管理公司" name="manageCompany" required>
              <dept-tree-select
                v-model="formData.manageCompany"
                placeholder="请选择物业管理公司"
                type="company"
              ></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="业务日期" name="bizDate" required>
              <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经办人" name="operator" required>
              <user-select v-model="formData.operator" placeholder="请选择经办人" title="请选择经办人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经办部门" name="operatorDepart" required>
              <dept-tree-select v-model="formData.operatorDepart" placeholder="请选择经办部门"></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="拆合类型" name="splitMergeType" required>
              <dict-select
                v-model="formData.splitMergeType"
                placeholder="请选择拆合类型"
                code="CT_BASE_ENUM_LeaseUnitSplitMergeBill_SplitMergeType"
              ></dict-select>
            </a-form-item>
          </a-col>
        </a-row>
        <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">拆合单元信息</h4>
        <lease-unit-transfer
          v-model:source-units="sourceUnits"
          v-model:target-units="targetUnits"
          :split-merge-type="formData.splitMergeType"
        />
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import {
  submitLeaseUnitSplitMergeBill,
  addLeaseUnitSplitMergeBill,
  editLeaseUnitSplitMergeBill,
  queryLeaseUnitSplitMergeBillEntryOriByMainId,
  queryLeaseUnitSplitMergeBillEntryDestByMainId
} from '../apis'
import LeaseUnitTransfer from './LeaseUnitTransfer.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const formRef = ref()
const confirmLoading = ref(false)

const rules = {
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  operator: [{ required: true, message: '请选择经办人', trigger: 'change' }],
  operatorDepart: [{ required: true, message: '请选择经办部门', trigger: 'change' }],
  splitMergeType: [{ required: true, message: '请选择拆合类型', trigger: 'change' }]
}

const formDataDefault = reactive({
  id: undefined,
  manageCompany: undefined,
  bizDate: undefined,
  operator: undefined,
  operator_dictText: undefined,
  operatorDepart: undefined,
  splitMergeType: 'Split',
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined,
  delFlag: undefined
})
const formData = reactive({ ...formDataDefault })

const sourceUnits = ref([])
const targetUnits = ref([])

/**
 * 打开编辑抽屉
 * @param {Object} data - 编辑数据，为空时表示新增
 */
const open = async (data) => {
  if (data) {
    Object.assign(formData, data)
    const oriRes = await queryLeaseUnitSplitMergeBillEntryOriByMainId({ id: data.id })
    const destRes = await queryLeaseUnitSplitMergeBillEntryDestByMainId({ id: data.id })
    sourceUnits.value = oriRes.result || []
    targetUnits.value = destRes.result || []
  }

  visible.value = true
}

/**
 * 取消编辑并重置表单
 */
const handleCancel = () => {
  visible.value = false
  Object.assign(formData, formDataDefault)
  sourceUnits.value = []
  targetUnits.value = []
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  emits('refresh')
}

/**
 * 保存表单（正式提交）
 */
const handleSave = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  await formRef.value.validate()

  if (sourceUnits.value.length === 0) {
    message.error('请至少选择一个源租赁单元')
    confirmLoading.value = false
    return
  }

  if (targetUnits.value.length === 0) {
    message.error('请至少添加一个目标租赁单元')
    confirmLoading.value = false
    return
  }

  const submitData = {
    ...formData,
    leaseUnitSplitMergeBillEntryOriList: sourceUnits.value.map((unit) => ({
      leaseUnit: unit.id,
      leaseUnitObject: { ...unit }
    })),
    leaseUnitSplitMergeBillEntryDestList: targetUnits.value.map((unit) => ({
      leaseUnit: unit.id,
      leaseUnitObject: { ...unit }
    }))
  }

  try {
    if (formData.id) {
      await editLeaseUnitSplitMergeBill(submitData)
      message.success('编辑成功')
    } else {
      const api = isTemporary ? addLeaseUnitSplitMergeBill : submitLeaseUnitSplitMergeBill
      const action = isTemporary ? '暂存' : '添加'
      await api(submitData)
      message.success(`${action}成功`)
    }
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

defineExpose({ open })
</script>

<style scoped>
.lease-unit-split-merge-edit-drawer :deep(.lease-unit-transfer) {
  margin-bottom: 24px;
}

.lease-unit-split-merge-edit-drawer :deep(.transfer-container) {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.lease-unit-split-merge-edit-drawer :deep(.transfer-list-item:hover) {
  background-color: #f5f5f5;
}
</style>
