<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}转账抵扣申请`"
    class="edit-transfer-deduction-drawer common-drawer"
    placement="right"
    width="1072px"
    :confirm-loading="confirmLoading"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">基础信息</h4>
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="业务时间" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>

        <a-form-item label="经办人" name="operator">
          <user-select v-model="formData.operator" placeholder="请选择经办人" />
        </a-form-item>

        <a-form-item label="业务部门" name="operatorDepart">
          <dept-tree-select v-model:value="formData.operatorDepart" placeholder="请选择业务部门" style="width: 100%" />
        </a-form-item>

        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="4" />
        </a-form-item>
      </a-form>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c] flex justify-between">
        <span>
          转款明细
          <span class="text-[12px] font-normal text-[#8992a3]">
            即：可用于抵扣的款项，转出用于抵扣其他未收取的应收款
          </span>
        </span>
        <a-button type="primary" @click="handleAddTransferItem">添加明细</a-button>
      </h4>

      <a-empty v-if="!formData.transferDeductionReqTransferDetailList.length" description="暂无数据"></a-empty>
      <a-table
        v-else
        :columns="transferColumns"
        :data-source="formData.transferDeductionReqTransferDetailList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'thisTransferOutAmount'">
            <a-input-number
              v-model:value="record.thisTransferOutAmount"
              :min="0"
              :precision="2"
              placeholder="请输入转款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-button type="link" danger @click="handleRemoveTransferItem(index)">
              <i class="a-icon-delete"></i>
            </a-button>
          </template>
        </template>
      </a-table>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c] flex justify-between">
        <span>
          抵扣欠款明细
          <span class="text-[12px] font-normal text-[#8992a3]">即：上述转出明细的总转出金额要抵扣的未收取营收明细</span>
        </span>
        <a-button type="primary" @click="handleAddDeductionItem">添加明细</a-button>
      </h4>
      <a-empty v-if="!formData.transferDeductionReqDeductionDetailList.length" description="暂无数据"></a-empty>
      <a-table
        v-else
        :columns="debtColumns"
        :data-source="formData.transferDeductionReqDeductionDetailList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'thisTransferIntoAmount'">
            <a-input-number
              v-model:value="record.thisTransferIntoAmount"
              :min="0"
              :precision="2"
              placeholder="请输入转款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-button type="link" danger @click="handleRemoveDebtItem(index)">
              <i class="a-icon-delete"></i>
            </a-button>
          </template>
        </template>
      </a-table>
      <div class="p-[16px] bg-[#f7f8fa] rounded-[8px] mt-[40px] border border-[#e6e9f0]">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-[24px] text-[#1d335c]">
              转出合计：
              <span class="text-[#f03a1d] font-bold">{{ transferOutTotal.toFixed(2) }}</span>
              转入合计：
              <span class="text-[#f03a1d] font-bold">{{ transferInTotal.toFixed(2) }}</span>
            </p>
            <p class="mt-[8px] text-[14px] text-[#495a7a]">{{ statusInfo }}</p>
          </div>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>

  <!-- 转款明细选择器 -->
  <transfer-detail-selector ref="transferDetailSelectorRef" @confirm="handleTransferDetailConfirm" />

  <!-- 抵扣欠款明细选择器 -->
  <deduction-detail-selector ref="deductionDetailSelectorRef" @confirm="handleDeductionDetailConfirm" />
</template>

<script setup>
import { message } from 'ant-design-vue'
import { editTransferDeduction, addTransferDeduction, submitTransferDeduction } from '../apis'
import TransferDetailSelector from './TransferDetailSelector.vue'
import DeductionDetailSelector from './DeductionDetailSelector.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const transferDetailSelectorRef = ref()
const deductionDetailSelectorRef = ref()

/**
 * 计算转出合计
 */
const transferOutTotal = computed(() => {
  return formData.transferDeductionReqTransferDetailList.reduce((total, item) => {
    return total + (item.thisTransferOutAmount || 0)
  }, 0)
})

/**
 * 计算转入合计
 */
const transferInTotal = computed(() => {
  return formData.transferDeductionReqDeductionDetailList.reduce((total, item) => {
    return total + (item.thisTransferIntoAmount || 0)
  }, 0)
})

/**
 * 计算状态文本
 */
const statusInfo = computed(() => {
  const outTotal = transferOutTotal.value
  const inTotal = transferInTotal.value
  const difference = outTotal - inTotal

  if (difference > 0) {
    return `已多收取：${difference.toFixed(2)}`
  }
  if (difference < 0) {
    return `合计欠款：${Math.abs(difference).toFixed(2)}`
  }
  return '已结清'
})

const formDataDefault = {
  bizDate: undefined,
  operatorDepart: undefined,
  operator: undefined,
  remark: undefined,
  transferDeductionReqTransferDetailList: [],
  transferDeductionReqDeductionDetailList: []
}
const formData = reactive({ ...formDataDefault })

const rules = {
  bizDate: [{ required: true, message: '请选择业务时间' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }],
  operator: [{ required: true, message: '请选择经办人' }]
}

/**
 * 转款明细表格列配置
 */
const transferColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract', width: 160, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余可转', dataIndex: 'residueTransferAmount', width: 120, fixed: 'right' },
  {
    title: '本次转款金额',
    dataIndex: 'thisTransferOutAmount',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => {
      return record.thisTransferOutAmount || 0
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 160,
    fixed: 'right'
  },
  { title: '操作', dataIndex: 'action', width: 80, fixed: 'right' }
]

/**
 * 抵扣欠款明细表格列配置
 */
const debtColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 160, fixed: 'left' },
  { title: '客户', dataIndex: 'customer', width: 150 },
  { title: '合同', dataIndex: 'contract', width: 150 },
  { title: '款项类型', dataIndex: 'paymentType', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 100 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余可转', dataIndex: 'residueDebtAmount', width: 120, fixed: 'right' },
  {
    title: '本次转款金额',
    dataIndex: 'thisTransferIntoAmount',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => {
      return record.thisTransferIntoAmount || 0
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 160,
    fixed: 'right'
  },
  { title: '操作', dataIndex: 'action', width: 80, fixed: 'right' }
]

/**
 * 打开编辑弹窗
 * @param {Object} data - 编辑的数据对象，新建时为空
 */
const open = (data) => {
  if (data && data.id) {
    Object.assign(formData, data)
  }

  visible.value = true
}

/**
 * 关闭抽屉并重置表单
 */
const handleCancel = () => {
  Object.assign(formData, formDataDefault)
  emits('refresh')
  visible.value = false
}

/**
 * 打开转款明细选择器
 */
const handleAddTransferItem = () => {
  transferDetailSelectorRef.value?.open()
}

/**
 * 转款明细选择确认回调
 * @param {Array} selectedData - 选中的明细数据
 */
const handleTransferDetailConfirm = (selectedData) => {
  const newItems = selectedData.map((item) => ({
    ...item,
    detailBill: item.number,
    detailBillEntry: item.id,
    thisTransferOutAmount: 0,
    residueTransferAmount: item.balance || 0,
    remark: ''
  }))

  formData.transferDeductionReqTransferDetailList.push(...newItems)
  message.success(`已添加 ${selectedData.length} 条转款明细`)
}

/**
 * 移除转款明细行
 * @param {number} index - 要移除的行索引
 */
const handleRemoveTransferItem = (index) => {
  formData.transferDeductionReqTransferDetailList.splice(index, 1)
}

/**
 * 打开抵扣欠款明细选择器
 */
const handleAddDeductionItem = () => {
  deductionDetailSelectorRef.value?.open()
}

/**
 * 抵扣欠款明细选择确认回调
 * @param {Array} selectedData - 选中的明细数据
 */
const handleDeductionDetailConfirm = (selectedData) => {
  const newItems = selectedData.map((item) => ({
    ...item,
    detailBill: item.number,
    detailBillEntry: item.id,
    thisTransferIntoAmount: 0,
    residueDebtAmount: item.residual || 0,
    remark: ''
  }))

  formData.transferDeductionReqDeductionDetailList.push(...newItems)
  message.success(`已添加 ${selectedData.length} 条抵扣欠款明细`)
}

/**
 * 移除抵扣欠款明细行
 * @param {number} index - 要移除的行索引
 */
const handleRemoveDebtItem = (index) => {
  formData.transferDeductionReqDeductionDetailList.splice(index, 1)
}

/**
 * 验证明细数据
 */
const validateDetails = () => {
  // 验证转款明细
  for (let i = 0; i < formData.transferDeductionReqTransferDetailList.length; i++) {
    const item = formData.transferDeductionReqTransferDetailList[i]
    if (!item.thisTransferOutAmount || item.thisTransferOutAmount <= 0) {
      message.error(`转款明细第 ${i + 1} 行的本次转款金额必须大于0`)
      return false
    }
  }

  // 验证抵扣欠款明细
  for (let i = 0; i < formData.transferDeductionReqDeductionDetailList.length; i++) {
    const item = formData.transferDeductionReqDeductionDetailList[i]
    if (!item.thisTransferIntoAmount || item.thisTransferIntoAmount <= 0) {
      message.error(`抵扣欠款明细第 ${i + 1} 行的本次转款金额必须大于0`)
      return false
    }
  }

  return true
}

/**
 * 保存表单数据
 * @param {boolean} isTemporary - 是否为暂存，true为暂存，false为提交
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  try {
    await formRef.value?.validate()

    // 验证明细数据
    if (!validateDetails()) {
      return
    }

    // 根据操作类型选择对应的API
    const api = formData.id ? editTransferDeduction : isTemporary ? addTransferDeduction : submitTransferDeduction

    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '添加'
    message.success(`转款抵扣申请${action}成功`)

    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 提交表单
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.edit-transfer-deduction-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>
