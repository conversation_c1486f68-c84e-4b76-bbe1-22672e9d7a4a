<template>
  <a-modal
    v-model:open="visible"
    :title="title"
    class="common-modal"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirm-loading="loading"
  >
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
      <a-form-item label="名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入名称" />
      </a-form-item>
      <a-form-item label="编码" name="number">
        <a-input v-model:value="formData.number" placeholder="请输入编码" />
      </a-form-item>
      <a-form-item label="上级分类" name="parent">
        <api-tree-select
          v-model:value="formData.parent"
          :async-fn="getWaterElectricityTableNumTree"
          placeholder="请选择上级分类"
          :disabled="!!currentParentId"
        />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formData.remark" placeholder="请输入备注" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import {
  addWaterElectricityTableNumTree,
  editWaterElectricityTableNumTree,
  getWaterElectricityTableNumTree
} from '../apis/waterElectricityTableNumTree'

const emits = defineEmits(['refresh'])

const formRef = ref()

const visible = ref(false)
const loading = ref(false)

const currentParentId = ref('')
const currentEditData = ref(null)

const formDataDefault = reactive({
  name: undefined,
  number: undefined,
  remark: undefined,
  parent: undefined
})
const formData = reactive({ ...formDataDefault })

// 校验规则
const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  number: [{ required: true, message: '请输入编码', trigger: 'blur' }]
}

const title = computed(() => {
  if (currentEditData.value?.id) return '编辑租赁单元'
  return currentParentId.value ? '添加下级节点' : '添加根节点'
})

const handleOk = async () => {
  await formRef.value.validate()
  loading.value = true
  // 如果是编辑模式，添加ID
  if (currentEditData.value?.id) {
    formData.id = currentEditData.value.id
  }
  try {
    const api = currentEditData.value?.id ? editWaterElectricityTableNumTree : addWaterElectricityTableNumTree
    await api(formData)

    message.success(`${currentEditData.value?.id ? '编辑' : '添加'}成功`)
    handleCancel()
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  visible.value = false
  emits('refresh')
}

/**
 * 打开弹窗
 * @param {Object} editData - 编辑时的数据
 * @param {String} parentId - 添加子节点时的父节点ID
 */
const open = (editData = null, parentId = '') => {
  currentEditData.value = editData
  currentParentId.value = parentId

  if (editData?.id) {
    Object.assign(formData, editData)
  } else {
    // 如果有父节点ID,设置父节点ID
    if (parentId) {
      formData.parent = parentId
    }
  }

  visible.value = true
}

defineExpose({ open })
</script>
