<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}退款申请`"
    class="edit-refund-drawer common-drawer"
    placement="right"
    width="1072px"
    :confirm-loading="confirmLoading"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">基础信息</h4>
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="业务时间" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>

        <a-form-item label="退款客户" name="customer">
          <customer-select v-model="formData.customer" placeholder="请选择退款客户" />
        </a-form-item>

        <a-form-item label="经办人" name="operator">
          <user-select v-model="formData.operator" placeholder="请选择经办人" />
        </a-form-item>

        <a-form-item label="业务部门" name="operatorDepart">
          <dept-tree-select v-model:value="formData.operatorDepart" placeholder="请选择业务部门" style="width: 100%" />
        </a-form-item>

        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="4" />
        </a-form-item>
      </a-form>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c] flex justify-between">
        退款明细
        <a-button type="primary" @click="handleAddRefundItem">添加明细</a-button>
      </h4>

      <a-empty v-if="!formData.refundReqBillEntryList.length" description="暂无数据"></a-empty>
      <a-table
        v-else
        :columns="refundColumns"
        :data-source="formData.refundReqBillEntryList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'thisRefundAmt'">
            <a-input-number
              v-model:value="record.thisRefundAmt"
              :min="0"
              :precision="2"
              placeholder="请输入退款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-button type="link" danger @click="handleRemoveRefundItem(index)">
              <i class="a-icon-delete"></i>
            </a-button>
          </template>
        </template>
      </a-table>

      <div class="p-[16px] bg-[#f7f8fa] rounded-[8px] mt-[40px] border border-[#e6e9f0]">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-[24px] text-[#1d335c]">
              <span v-if="refundTotal < 0">应退：</span>
              <span v-else>调整后待核销合计：</span>
              <span class="text-[#f03a1d] font-bold">{{ refundTotal.toFixed(2) }}</span>
            </p>
          </div>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>

  <!-- 退款明细选择器 -->
  <refund-detail-selector ref="refundDetailSelectorRef" @confirm="handleRefundDetailConfirm" />
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addRefundReqBill, editRefundReqBill, submitRefundReqBill } from '../apis'
import RefundDetailSelector from './RefundDetailSelector.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const refundDetailSelectorRef = ref()

/**
 * 计算退款金额合计
 */
const refundTotal = computed(() => {
  return formData.refundReqBillEntryList.reduce((total, item) => {
    return total + (item.thisRefundAmt || 0)
  }, 0)
})

const formDataDefault = {
  bizDate: undefined,
  customer: undefined,
  operator: undefined,
  operatorDepart: undefined,
  remark: undefined,
  refundReqBillEntryList: []
}

const formData = reactive({ ...formDataDefault })

const rules = {
  bizDate: [{ required: true, message: '请选择业务日期' }],
  customer: [{ required: true, message: '请选择退款客户', trigger: 'submit' }],
  operator: [{ required: true, message: '请选择经办人', trigger: 'submit' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }]
}

/**
 * 退款明细表格列配置
 */
const refundColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 160, fixed: 'left' },
  { title: '客户', dataIndex: 'customer', width: 150 },
  { title: '合同', dataIndex: 'contract', width: 150 },
  { title: '款项类型', dataIndex: 'paymentType', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 100 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余可转', dataIndex: 'residueRefundAmount', width: 120, fixed: 'right' },
  {
    title: '本次转款金额',
    dataIndex: 'thisRefundAmt',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => {
      return record.thisRefundAmt || 0
    }
  },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' },
  { title: '操作', dataIndex: 'action', width: 80, fixed: 'right' }
]

/**
 * 打开抽屉并初始化表单数据
 * @param {Object} record - 编辑时的记录数据，新建时为空
 */
const open = (record) => {
  visible.value = true
  if (record?.id) {
    Object.assign(formData, record)
  }
}

/**
 * 取消操作，关闭抽屉并重置表单
 */
const handleCancel = () => {
  Object.assign(formData, formDataDefault)
  emits('refresh')
  visible.value = false
}

/**
 * 打开退款明细选择器
 */
const handleAddRefundItem = () => {
  refundDetailSelectorRef.value?.open()
}

/**
 * 退款明细选择确认回调
 * @param {Array} selectedData - 选中的明细数据
 */
const handleRefundDetailConfirm = (selectedData) => {
  const newItems = selectedData.map((item) => ({
    ...item,
    detailBill: item.number,
    detailBillEntry: item.id,
    thisRefundAmt: 0,
    residueRefundAmount: item.balance || 0,
    remark: ''
  }))

  formData.refundReqBillEntryList.push(...newItems)
  message.success(`已添加 ${selectedData.length} 条退款明细`)
}

/**
 * 删除退款明细项
 * @param {number} index - 要删除的行索引
 */
const handleRemoveRefundItem = (index) => {
  formData.refundReqBillEntryList.splice(index, 1)
}

/**
 * 验证明细数据
 */
const validateDetails = () => {
  // 验证退款明细
  for (let i = 0; i < formData.refundReqBillEntryList.length; i++) {
    const item = formData.refundReqBillEntryList[i]
    if (!item.thisRefundAmt || item.thisRefundAmt <= 0) {
      message.error(`退款明细第 ${i + 1} 行的本次退款金额必须大于0`)
      return false
    }
  }

  return true
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  try {
    await formRef.value?.validate()

    // 验证明细数据
    if (!validateDetails()) {
      return
    }

    // 根据操作类型选择对应的API
    const api = formData.id ? editRefundReqBill : isTemporary ? addRefundReqBill : submitRefundReqBill

    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '添加'
    message.success(`退款申请${action}成功`)

    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 提交退款申请
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存退款申请
 */
const handleTemporaryStorage = () => saveData(true)

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.edit-refund-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>
