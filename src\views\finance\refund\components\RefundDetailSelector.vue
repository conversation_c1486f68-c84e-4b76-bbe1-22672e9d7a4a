<template>
  <a-modal
    v-model:open="visible"
    title="选择退款明细"
    width="1200px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex mb-[16px]">
      <s-input v-model="searchParams.number" placeholder="搜索单据ID" class="!w-[280px]" @input="handleInput"></s-input>
      <filter-more
        :params="searchParams"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></filter-more>
    </div>

    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: 'checkbox'
      }"
      :scroll="{ y: '50vh', x: 1800 }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { f7RefundDetailList } from '../apis'

const emit = defineEmits(['confirm'])

const visible = ref(false)
let timer

const searchParams = reactive({
  number: undefined,
  customer: undefined,
  contract: undefined,
  leaseUnit: undefined,
  paymentType: undefined,
  status: undefined,
  receiveDate: undefined
})

const searchList = [
  { label: '客户名称', name: 'customer', type: 's-input', placeholder: '请输入客户名称' },
  { label: '合同', name: 'contract', type: 's-input', placeholder: '请输入合同' },
  { label: '租赁单元', name: 'leaseUnit', type: 's-input', placeholder: '请输入租赁单元' },
  { label: '款项类型', name: 'paymentType', type: 's-input', placeholder: '请输入款项类型' },
  {
    label: '状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_AuditStatus',
    placeholder: '请选择状态'
  },
  { label: '应收日期', name: 'receiveDate', type: 'date', placeholder: '请选择应收日期' }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(f7RefundDetailList)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id', false)

/**
 * 弹窗表格列配置
 */
const columns = [
  { title: '单据ID', dataIndex: 'parent', width: 200, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract', width: 160, ellipsis: true },
  { title: '租赁单元', dataIndex: 'leaseUnit', width: 160, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType', width: 120 },
  { title: '状态', dataIndex: 'status', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '调整金额', dataIndex: 'receiveAmountAdjust', width: 120 },
  { title: '应收金额', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '剩余金额', dataIndex: 'residual', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '结清日期', dataIndex: 'receiveEndDate', width: 120 }
]

/**
 * 打开选择器
 */
const open = () => {
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条明细记录')
    return
  }

  emit('confirm', selectedRows.value)
  handleCancel()
}

/**
 * 搜索输入处理
 */
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 表格分页变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

defineExpose({
  open
})
</script>
