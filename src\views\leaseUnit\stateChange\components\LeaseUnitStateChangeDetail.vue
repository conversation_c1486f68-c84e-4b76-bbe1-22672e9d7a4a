<template>
  <a-drawer
    v-model:open="visible"
    :loading="loading"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    :title="detailData.name"
    :mask-closable="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 || dataList.length === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span
            v-if="detailData.status === 'TEMP'"
            class="primary-btn"
            :class="{ 'cursor-not-allowed': loading }"
            @click="handleSubmit"
          >
            <i class="a-icon-check mr-1"></i>
            提交
          </span>
          <span v-else class="primary-btn" :class="{ 'cursor-not-allowed': loading }" @click="handleBack">
            <i class="a-icon-rollback mr-1"></i>
            撤回
          </span>
          <span class="primary-btn" :class="{ 'cursor-not-allowed': loading }" @click="handleUrge">
            <i class="a-icon-clock-circle mr-1"></i>
            催办
          </span>
        </div>
      </div>
    </template>
    <div class="flex items-center mb-[12px]">
      <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">租赁单元状态变更</h2>
      <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_AuditStatus"></status-tag>
    </div>
    <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
      编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
    </div>

    <anchor-tabs :tab-list="tabList" class="h-[calc(100vh-284px)]">
      <template #baseInfo>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">管理公司: {{ detailData.manageCompany_dictText || '-' }}</span>
          <span class="w-[50%]">变更目标状态: {{ detailData.destStatus_dictText || '-' }}</span>
          <span class="w-[50%]">业务日期: {{ detailData.bizDate }}</span>
          <span class="w-[100%]">变更说明: {{ detailData.remark || '-' }}</span>
        </div>
      </template>
      <template #leaseUnit>
        <a-table
          class="lease-unit-table"
          :columns="leaseUnitColumns"
          :data-source="leaseUnitList"
          :pagination="false"
          row-key="id"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'bizStatus'">
              <a-tag color="success" v-if="record.bizStatus === '在租'">{{ record.bizStatus }}</a-tag>
              <a-tag v-else>{{ record.bizStatus }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <span class="primary-btn" @click="handleViewLeaseUnitDetail(record)">单元详情</span>
            </template>
          </template>
        </a-table>
      </template>
    </anchor-tabs>
  </a-drawer>

  <!-- 租赁单元详情抽屉 -->
  <lease-unit-detail ref="leaseUnitDetailRef" :data-list="leaseUnitList" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import {
  getLeaseUnitStateChangeReqBillDetail,
  getLeaseUnitStateChangeReqBillById,
  submitLeaseUnitStateChangeReqBill
} from '../apis'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'

const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const visible = ref(false)
const loading = ref(false)
const activeSection = ref('baseInfo')

const detailData = ref({})
const currentId = ref('')
const leaseUnitList = ref([])

const leaseUnitDetailRef = ref(null)

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return dataList.findIndex((i) => i.id === detailData.value.id)
})

const tabList = [
  { title: '基础信息', name: 'baseInfo' },
  { title: '变更单元', name: 'leaseUnit' }
]

const leaseUnitColumns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'leaseUnitAddress', width: 160, ellipsis: true },
  { title: '租赁归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', width: 160, ellipsis: true },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 160, ellipsis: true },
  { title: '原业务状态', dataIndex: 'bizStatus', width: 120 },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

// 切换详情
const handleSwitchDetail = (index) => {
  loadDetail(dataList[index].id)
}

// 获取详情数据
const loadDetail = async (id) => {
  loading.value = true
  const res = await getLeaseUnitStateChangeReqBillDetail({ id })
  if (res.success) {
    detailData.value = res.result || {}
    const { result } = await getLeaseUnitStateChangeReqBillById({ id })
    leaseUnitList.value = result?.length ? result : []
  } else {
    message.error(res.message || '获取详情失败')
  }
  loading.value = false
}

// 撤回
const handleBack = () => {
  Modal.confirm({
    title: '确认撤回',
    content: '确定要撤回该状态变更单吗？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      message.warn('等接口')
      handleClose()
    }
  })
}

// 提交
const handleSubmit = () => {
  if (loading.value) return
  Modal.confirm({
    title: '确认提交',
    content: '确定要提交该状态变更单吗？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        // 获取最新的租赁单元列表
        const { result } = await getLeaseUnitStateChangeReqBillById({ id: detailData.value.id })
        if (result?.length) {
          detailData.value.leaseUnitStateChangeReqBillEntryList = result
        }
        // 提交变更申请
        await submitLeaseUnitStateChangeReqBill(detailData.value)
        message.success('提交成功')
        handleClose()
      } catch (error) {
        message.error(error.message || '提交失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 催办
const handleUrge = () => {
  message.success('催办成功')
}

// 查看租赁单元详情
const handleViewLeaseUnitDetail = (record) => {
  if (record?.id) {
    leaseUnitDetailRef.value.open(record)
  } else {
    message.error('租赁单元信息不完整，无法查看详情')
  }
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
  detailData.value = {}
  currentId.value = ''
  currentIndex.value = 0
  activeSection.value = 'baseInfo'
}

// 打开详情抽屉
const open = async (record) => {
  if (!record?.id) {
    message.error('缺少必要参数')
    return
  }
  currentId.value = record.id
  visible.value = true
  // 找到当前记录在列表中的索引
  const index = dataList.findIndex((item) => item.id === record.id)
  if (index !== -1) {
    currentIndex.value = index
  }
  await loadDetail(record.id)
}

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.lease-unit-table {
  margin-top: 16px;

  :deep(.ant-table-thead > tr > th) {
    background-color: #f5f5f5;
    font-weight: 500;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f0f7ff;
  }

  :deep(.ant-tag) {
    border-radius: 4px;
  }
}
</style>
