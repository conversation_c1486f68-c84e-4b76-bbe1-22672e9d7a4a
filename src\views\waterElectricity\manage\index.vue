<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[24px] mb-[16px]">
      <div class="flex items-center">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete" @click="handleBatchDelete">删除</a-menu-item>
              <a-menu-item key="batchEnable" @click="handleBatchStatus('ENABLE')">启用</a-menu-item>
              <a-menu-item key="batchDisable" @click="handleBatchStatus('DISABLE')">禁用</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <water-electricity-table-num-tree-filter @treeNodeChange="handleTreeNodeChange" />
        <s-input
          v-model="searchParams.name"
          placeholder="搜索名称"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch :checked="record.status === 'ENABLE'" @change="handleStatusChange(record)" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" @click="handleEdit(record)">编辑</a-menu-item>
                <a-menu-item key="delete" @click="handleDelete(record)">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('水电表导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
    <edit-water-electricity ref="editDrawerRef" @refresh="onTableChange" />
    <water-electricity-detail ref="detailDrawerRef" :data-list="list" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import EditWaterElectricity from './components/EditWaterElectricity.vue'
import WaterElectricityDetail from './components/WaterElectricityDetail.vue'
import WaterElectricityTableNumTreeFilter from './components/WaterElectricityTableNumTreeFilter.vue'
import {
  getWaterElectricityList,
  deleteWaterElectricity,
  batchDeleteWaterElectricity,
  updateEnableDisableStatus,
  exportExcel,
  importExcel
} from './apis/waterElectricity'

const route = useRoute()

const columnSetRef = ref()
const editDrawerRef = ref()
const detailDrawerRef = ref()
const commonImportRef = ref()

const exportLoading = ref(false)

// 搜索参数
const searchParams = reactive({
  name: undefined,
  number: undefined,
  type: undefined,
  property: undefined,
  doubleRate: undefined,
  status: undefined,
  ownerCompany: undefined,
  collectionCompany: undefined,
  manageCompany: undefined,
  price: undefined,
  remark: undefined,
  treeId: undefined
})

const searchList = reactive([
  { label: '编码(表号)', name: 'number', type: 's-input', placeholder: '请输入编码(表号)' },
  {
    label: '类型',
    name: 'type',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_WaterElectriCityTableNum_Type',
    placeholder: '请选择类型'
  },
  {
    label: '属性',
    name: 'property',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_WaterElectriCityTableNum_Property',
    placeholder: '请选择属性'
  },
  { label: '倍率', name: 'doubleRate', type: 'input', placeholder: '请输入倍率' },
  {
    label: '状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_BaseStatus',
    placeholder: '请选择状态'
  },
  { label: '资产权属公司', name: 'ownerCompany', type: 'company-select', placeholder: '请选择资产权属公司' },
  { label: '租金归集公司', name: 'collectionCompany', type: 'company-select', placeholder: '请选择租金归集公司' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择物业管理公司' },
  { label: '单价', name: 'price', type: 'input', placeholder: '请输入单价' },
  { label: '备注', name: 'remark', type: 's-input', placeholder: '请输入备注' }
])

// 表格列定义
const defaultColumns = [
  { title: '名称', dataIndex: 'name', fixed: 'left', width: 200 },
  { title: '编码(表号)', dataIndex: 'number', width: 160 },
  { title: '类型', dataIndex: 'type_dictText', width: 120 },
  { title: '属性', dataIndex: 'property_dictText', width: 120 },
  { title: '倍率', dataIndex: 'doubleRate', width: 120 },
  { title: '状态', dataIndex: 'status', width: 120 },
  { title: '资产权属公司', dataIndex: 'ownerCompany_dictText', width: 160, ellipsis: true },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '组别', dataIndex: 'treeId_dictText', width: 120 },
  { title: '单价', dataIndex: 'price', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

const pageTitle = computed(() => route.meta.title)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getWaterElectricityList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list)

// 表格变化事件
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

// 搜索输入防抖
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 新增水电表
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 编辑水电表
 */
const handleEdit = (record) => {
  editDrawerRef.value.open(record)
}

/**
 * 查看水电表详情
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出水电表数据
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('水电表清单.xls', searchParams)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

/**
 * 删除水电表
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除"${record.name}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteWaterElectricity({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除水电表
 */
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要删除的数据')
    return
  }
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的${selectedRowKeys.value.length}条数据吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteWaterElectricity({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 更新状态
 */
const handleStatusChange = async (record) => {
  await updateEnableDisableStatus({ ids: record.id, status: record.status === 'DISABLE' ? 'ENABLE' : 'DISABLE' })
  message.success(`${record.status === 'DISABLE' ? '启用' : '禁用'}成功`)
  onTableChange()
}

/**
 * 批量更新状态
 */
const handleBatchStatus = (status) => {
  if (!selectedRowKeys.value.length) {
    message.warning(`请选择要${status === 'ENABLE' ? '启用' : '禁用'}的数据`)
    return
  }
  Modal.confirm({
    title: `确认批量${status === 'ENABLE' ? '启用' : '禁用'}`,
    content: `确定要${status === 'ENABLE' ? '启用' : '禁用'}选中的${selectedRowKeys.value.length}条数据吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await updateEnableDisableStatus({ ids: selectedRowKeys.value.join(','), status })
      message.success(`批量${status === 'ENABLE' ? '启用' : '禁用'}成功`)
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 处理树节点变化
 */
const handleTreeNodeChange = (nodeId) => {
  searchParams.treeId = nodeId
  onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
}

// 初始加载
onMounted(() => {
  onTableChange()
})
</script>
