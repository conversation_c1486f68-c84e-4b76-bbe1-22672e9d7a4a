<template>
  <div>
    <h2 class="text-[18px] font-bold mb-[20px]">消息模板管理</h2>
    <div class="flex justify-between my-[16px]">
      <div>
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete" @click="handleBatchDelete">删除</a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-1"></i>
          </a-button>
        </a-dropdown>
        <s-input
          v-model="searchParams.templateTitle"
          placeholder="搜索模板标题"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'templateContent'">
          <div class="line-clamp-2" :title="record.templateContent">{{ record.templateContent }}</div>
        </template>
        <template v-if="column.dataIndex === 'isEnabled'">
          <a-switch :checked="record.isEnabled === '1'" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" @click="handleEdit(record)">编辑</a-menu-item>
                <a-menu-item key="delete" @click="handleDelete(record)">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('消息模板导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
    <edit-template ref="editTemplateRef" @refresh="onTableChange" />
    <template-detail ref="templateDetailRef" @refresh="onTableChange" :data-list="list" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import EditTemplate from './components/EditTemplate.vue'
import TemplateDetail from './components/TemplateDetail.vue'
import { getTemplateList, deleteTemplate, batchDeleteTemplate, updateStatus } from './apis'

const columnSetRef = ref()
const editTemplateRef = ref()
const templateDetailRef = ref()
const commonImportRef = ref()

// 搜索参数
const searchParams = reactive({
  templateTitle: undefined,
  templateCode: undefined,
  templateType: undefined,
  isEnabled: undefined
})

// 表格数据和分页
const { list, pagination, tableLoading, onTableFetch } = usePageTable(getTemplateList)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

// 默认表格列定义
const defaultColumns = [
  { title: '模板编码', dataIndex: 'templateCode', fixed: 'left', width: 150 },
  { title: '模版标题', dataIndex: 'templateTitle', width: 200 },
  { title: '模版内容', dataIndex: 'templateContent', width: 300 },
  { title: '模版类型', dataIndex: 'templateType', width: 120 },
  { title: '是否启用', dataIndex: 'isEnabled', width: 100 },
  { title: '创建人', dataIndex: 'createBy', width: 100 },
  { title: '创建时间', dataIndex: 'createTime', width: 150 },
  { title: '更新人', dataIndex: 'updateBy', width: 100 },
  { title: '更新时间', dataIndex: 'updateTime', width: 150 },
  { title: '操作', dataIndex: 'action', width: 150, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

/**
 * 新增模板
 */
const handleAdd = () => {
  editTemplateRef.value.open()
}

/**
 * 编辑模板
 */
const handleEdit = (record) => {
  editTemplateRef.value.open(record.id)
}

/**
 * 查看模板详情
 */
const handleDetail = (record) => {
  templateDetailRef.value.open(record.id)
}

/**
 * 导入模板
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出模板
 */
const handleExport = () => {
  message.success('导出功能已触发')
}

/**
 * 更新模板状态
 */
const handleStatusChange = async (record, checked) => {
  try {
    await updateStatus({
      isEnabled: checked
    })
    message.success('状态更新成功')
    record.isEnabled = checked
  } catch {
    message.error('状态更新失败')
  }
}

/**
 * 删除单个模板
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板"${record.templateTitle}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteTemplate({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除模板
 */
const handleBatchDelete = () => {
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的模板')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个模板吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteTemplate({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      onTableChange()
    }
  })
}

/**
 * 表格变化处理
 */
const onTableChange = ({ pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo, pageSize, ...searchParams })
}

/**
 * 搜索输入处理
 */
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

// 模拟导入导出函数
const importExcel = () => {
  return Promise.resolve({
    result: '导入成功'
  })
}

const exportExcel = () => {
  return Promise.resolve()
}

onMounted(() => {
  onTableChange()
})
</script>
