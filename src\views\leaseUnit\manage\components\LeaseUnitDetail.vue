<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 || dataList.length === 0 }"
            @click="handleSwitchLeaseUnit(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchLeaseUnit(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="1" @click="handleStateChange">业务状态变更</a-menu-item>
                <a-menu-item key="2" @click="handleUnAudit" :disabled="detailData.status !== '已审批'">
                  反审批
                </a-menu-item>
                <a-menu-item key="3" @click="handleDelete">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">租赁单元详情: {{ detailData.name }}</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_BaseStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #baseInfo>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">单元名称: {{ detailData.name || '-' }}</span>
            <span class="w-[50%]">房屋产权: {{ detailData.houseOwner_dictText || '-' }}</span>
            <span class="w-[50%]">
              关联项目楼栋: {{ detailData.wyProject_dictText || '-' }}/{{ detailData.wyBuilding_dictText || '-' }}/{{
                detailData.wyFloor_dictText || '-'
              }}
            </span>
            <span class="w-[50%]">产权用途: {{ detailData.propertyUse_dictText || '-' }}</span>
            <span class="w-[50%]">使用权类型: {{ detailData.landNature_dictText || '-' }}</span>
            <span class="w-[50%]">
              地址: {{ detailData.province || '-' }}{{ detailData.city || '-' }}{{ detailData.area || '-'
              }}{{ detailData.detailAddress || '-' }}
            </span>
            <span class="w-[50%]">资产类型: {{ detailData.assetType_dictText || '-' }}</span>
            <span class="w-[50%]">归集公司: {{ detailData.collectionCompany_dictText || '-' }}</span>
            <span class="w-[50%]">来源租赁单元: {{ detailData.sourceLeaseUnit || '-' }}</span>
            <span class="w-[50%]">管理公司: {{ detailData.manageCompany_dictText || '-' }}</span>
            <span class="w-[50%]">权属公司: {{ detailData.ownerCompany_dictText || '-' }}</span>
            <span class="w-[50%]">业务状态: {{ detailData.bizStatus_dictText || '-' }}</span>
            <span class="w-full">备注: {{ detailData.remark || '-' }}</span>
          </div>

          <div class="mt-[24px]">
            <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">租赁信息</h4>
            <div class="flex flex-wrap gap-y-[12px] text-secondary">
              <span class="w-[50%]">使用类型: {{ detailData.useType_dictText || '-' }}</span>
              <span class="w-[50%]">租赁面积(㎡): {{ detailData.leaseArea || '-' }}</span>
              <span class="w-[50%]">租赁用途: {{ detailData.leaseUse_dictText || '-' }}</span>
              <span class="w-[50%]">控制方式: {{ detailData.controlType || '-' }}</span>
              <span class="w-[50%]">生效日期: {{ detailData.effectDate || '-' }}</span>
              <span class="w-[50%]">到期日期: {{ detailData.expireDate || '-' }}</span>
              <span class="w-[50%]">片区管理员: {{ detailData.areaManager_dictText || '-' }}</span>
            </div>
          </div>
        </template>

        <template #buildingInfo>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">建筑面积(㎡): {{ detailData.structureArea || '-' }}</span>
            <span class="w-[50%]">宗地面积(㎡): {{ detailData.floorArea || '-' }}</span>
            <span class="w-[50%]">公摊面积(㎡): {{ detailData.publicArea || '-' }}</span>
            <span class="w-[50%]">层高(m): {{ detailData.layerHight || '-' }}</span>
            <span class="w-[50%]">层数/总层数: {{ detailData.layerNum || '-' }}</span>
            <span class="w-[50%]">建成年份: {{ detailData.buildYear || '-' }}</span>
            <span class="w-[50%]">建筑结构: {{ detailData.buildStructrue_dictText || '-' }}</span>
            <span class="w-[50%]">户型: {{ detailData.houseModel || '-' }}</span>
            <span class="w-[50%]">消防等级: {{ detailData.firefightingRate_dictText || '-' }}</span>
            <span class="w-[50%]">房屋安全等级: {{ detailData.houseSafeRate_dictText || '-' }}</span>
          </div>
        </template>

        <template #taxInfo>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">房产税计税原值: {{ detailData.houseTaxOrgValue || '-' }}</span>
            <span class="w-[50%]">开票增值税率(%): {{ detailData.addTaxRate || '-' }}</span>
            <span class="w-[50%]">发票地址: {{ detailData.invoiceAddress || '-' }}</span>
          </div>
        </template>

        <template #meterInfo>
          <div>
            <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">水表信息</h4>
            <a-table :columns="waterMeterColumns" :data-source="waterMeterData" :pagination="false" size="small">
              <template #bodyCell="{ column }">
                <template v-if="column.dataIndex === 'action'">
                  <a>查看</a>
                </template>
              </template>
            </a-table>

            <h4 class="text-[16px] font-bold mb-[12px] mt-[24px] text-[#1d335c]">电表信息</h4>
            <a-table :columns="electricMeterColumns" :data-source="electricMeterData" :pagination="false" size="small">
              <template #bodyCell="{ column }">
                <template v-if="column.dataIndex === 'action'">
                  <a>查看</a>
                </template>
              </template>
            </a-table>
          </div>
        </template>
        <template #attachmentInfo-title>
          <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">附件信息</h4>
        </template>
        <template #attachmentInfo>
          <file-list v-if="detailData.id" :biz-id="detailData.id" />
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <edit-lease-unit ref="editDrawerRef" @refresh="refreshData"></edit-lease-unit>

  <edit-lease-unit-state-change ref="editStateChangeDrawerRef" @refresh="refreshData"></edit-lease-unit-state-change>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import EditLeaseUnit from './EditLeaseUnit.vue'
import EditLeaseUnitStateChange from './EditLeaseUnitStateChange.vue'
import { getLeaseUnitDetail, deleteLeaseUnit, unAudit } from '../apis/leaseUnit'

const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const emits = defineEmits(['refresh', 'edit', 'editStatusChange'])

const visible = ref(false)
const loading = ref(false)
const editDrawerRef = ref()
const editStateChangeDrawerRef = ref()
const currentId = ref('')
const detailData = ref({})
const waterMeterData = ref([])
const electricMeterData = ref([])

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return dataList.findIndex((i) => i.id === detailData.value.id)
})

const tabList = [
  { name: 'baseInfo', title: '基础信息' },
  { name: 'buildingInfo', title: '建筑信息' },
  { name: 'taxInfo', title: '税务信息' },
  { name: 'meterInfo', title: '水电表信息' },
  { name: 'attachmentInfo', title: '附件信息' }
]

const waterMeterColumns = [
  { title: '名称', dataIndex: 'name', key: 'name', fixed: 'left', width: 200 },
  { title: '编号', dataIndex: 'number', key: 'number', width: 160 },
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '倍率', dataIndex: 'ratio', key: 'ratio', width: 120 },
  { title: '操作', dataIndex: 'action', key: 'action', width: 140 }
]

const electricMeterColumns = [
  { title: '名称', dataIndex: 'name', key: 'name', fixed: 'left', width: 200 },
  { title: '编号', dataIndex: 'number', key: 'number', width: 160 },
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '倍率', dataIndex: 'ratio', key: 'ratio', width: 120 },
  { title: '操作', dataIndex: 'action', key: 'action', width: 140 }
]

/**
 * 打开详情抽屉
 * @param {Object} record - 租赁单元记录
 */
const open = async (record) => {
  if (!record || !record.id) return
  visible.value = true
  currentId.value = record.id
  loading.value = true
  try {
    await fetchDetail(record.id)
  } finally {
    loading.value = false
  }
}

/**
 * 关闭抽屉
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
  currentId.value = ''
}

/**
 * 切换租赁单元（上一条/下一条）
 * @param {number} index - 目标租赁单元在列表中的索引
 */
const handleSwitchLeaseUnit = (index) => {
  if (index < 0 || index >= dataList.length) return
  loadDetail(dataList[index].id)
}

/**
 * 编辑租赁单元
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 业务状态变更
 */
const handleStateChange = () => {
  editStateChangeDrawerRef.value.open([detailData.value])
}

/**
 * 删除租赁单元
 */
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除租赁单元"${detailData.value.name || detailData.value.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteLeaseUnit({ id: currentId.value })
      message.success('删除成功')
      visible.value = false
      emits('refresh')
    }
  })
}

/**
 * 刷新当前详情数据
 */
const refreshData = async () => {
  await loadDetail(detailData.value.id)
  emits('refresh')
}

/**
 * 加载详情数据
 * @param {string} id - 租赁单元ID
 */
const loadDetail = async (id) => {
  loading.value = true
  await fetchDetail(id)
  loading.value = false
}

/**
 * 获取详情数据
 * @param {string} id - 租赁单元ID
 */
const fetchDetail = async (id) => {
  loading.value = true
  const res = await getLeaseUnitDetail({ id })
  if (res.success) {
    detailData.value = res.result || {}

    // 处理水电表数据
    if (res.result.waterElectricList && res.result.waterElectricList.length) {
      waterMeterData.value = res.result.waterElectricList
        .filter((item) => item.type === '水表')
        .map((item, index) => ({
          ...item,
          key: `water_${index}`
        }))

      electricMeterData.value = res.result.waterElectricList
        .filter((item) => item.type === '电表')
        .map((item, index) => ({
          ...item,
          key: `electric_${index}`
        }))
    } else {
      waterMeterData.value = []
      electricMeterData.value = []
    }
  } else {
    message.error(res.message || '获取详情失败')
  }
  loading.value = false
}

/**
 * 反审批操作
 */
const handleUnAudit = () => {
  if (detailData.value.status !== '已审批') {
    message.warning('只有已审批状态的租赁单元才能进行反审批操作')
    return
  }

  Modal.confirm({
    title: '确认反审批',
    content: `确定要对租赁单元"${detailData.value.name || detailData.value.number}"执行反审批操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await unAudit({ id: currentId.value })
      message.success('反审批成功')
      await fetchDetail(currentId.value)
    }
  })
}

defineExpose({
  open
})
</script>
