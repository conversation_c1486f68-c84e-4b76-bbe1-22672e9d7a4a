<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 || dataList.length === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="delete" @click="handleDelete">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">客户详情: {{ detailData.name }}</h2>
        <status-tag
          :dict-value="detailData.customerStatus"
          dict-code="CT_BASE_ENUM_Customer_CustomerStatus"
        ></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #basic>
          <div class="flex flex-col gap-y-[40px]">
            <div>
              <div class="flex flex-wrap gap-y-[12px] text-secondary">
                <span class="w-[50%]">客户名称：{{ detailData.name || '-' }}</span>
                <span class="w-[50%]">
                  是否集团内公司：{{ detailData.isInternalCompany ? '是' : '否' }}
                  <span v-if="detailData.isInternalCompany">（{{ detailData.internalCompany_dictText }}）</span>
                </span>
                <span class="w-[50%]">管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
                <span class="w-[50%]">客户来源：{{ detailData.customerSource_dictText || '-' }}</span>
                <span class="w-[50%]">客户类型：{{ detailData.customerType_dictText || '-' }}</span>
                <span class="w-[50%]">营业执照：{{ busiLicenceFileName || '-' }}</span>
                <span class="w-[50%]">营业执照号：{{ detailData.busiLicenceNum || '-' }}</span>
                <span class="w-[50%]">法人/自然人：{{ detailData.legalPerson || '-' }}</span>
                <span class="w-[50%]">法人身份证：{{ detailData.legalPerson || '-' }}</span>
                <span class="w-[50%]">注册地址：{{ detailData.registeredAddress || '-' }}</span>
                <span class="w-[50%]">履约情况：{{ detailData.performance_dictText || '-' }}</span>
                <span class="w-[50%]">安全等级：{{ detailData.safeRate_dictText || '-' }}</span>
              </div>
            </div>

            <!-- 客户需求 -->
            <div>
              <h4 class="text-[16px] font-bold mb-[12px]">客户需求</h4>
              <div class="flex flex-wrap gap-y-[12px] text-secondary">
                <span class="w-[50%]">维护日期：{{ detailData.maintainDate || '-' }}</span>
                <span class="w-[50%]">维护人员：{{ detailData.maintainPerson_dictText || '-' }}</span>
                <span class="w-[100%]">初步需求：{{ detailData.initRequire || '-' }}</span>
              </div>
            </div>

            <!-- 联系信息 -->
            <div>
              <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">联系信息</h4>
              <div class="flex flex-wrap gap-y-[12px] text-secondary">
                <span class="w-[50%]">联系人：{{ detailData.linkman || '-' }}</span>
                <span class="w-[50%]">法定送达地址：{{ detailData.linkDetailAddress || '-' }}</span>
                <span class="w-[50%]">联系电话：{{ detailData.linkmanPhone || '-' }}</span>
                <span class="w-[50%]">推送手机：{{ detailData.pushMobile || '-' }}</span>
                <span class="w-[50%]">邮箱：{{ detailData.email || '-' }}</span>
              </div>
            </div>
          </div>
        </template>

        <template #financial>
          <div>
            <div class="flex flex-wrap gap-y-[12px] text-secondary">
              <span class="w-[50%]">开户银行：{{ detailData.depositBank || '-' }}</span>
              <span class="w-[50%]">开户行账号：{{ detailData.depositBankAccount || '-' }}</span>
              <span class="w-[50%]">发票名称：{{ detailData.invoiceName || '-' }}</span>
              <span class="w-[50%]">发票类型：{{ detailData.invoiceType_dictText || '-' }}</span>
              <span class="w-[50%]">税率(%)：{{ detailData.taxRate || '-' }}</span>
            </div>
          </div>
        </template>

        <template #followRecord-title>
          <h4 class="text-[16px] font-bold mb-[12px] flex justify-between">
            跟进记录
            <a-button type="primary" @click="handleAddFollowRecord">添加记录</a-button>
          </h4>
        </template>
        <template #followRecord>
          <a-table
            :data-source="list"
            :columns="followRecordColumns"
            :loading="tableLoading"
            :pagination="pagination"
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'followContent'">
                <span>{{ record.layoutPreference }}/{{ record.supportFacilityRequire }}/{{ record.otherContent }}</span>
              </template>
            </template>
          </a-table>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <edit-customer ref="editDrawerRef" @refresh="refreshData" />

  <edit-follow-record ref="editFollowRecordModalRef" @refresh="onTableChange" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import { getAttachmentByIds } from '@/apis/common'
import { getFollowRecordList } from '@/views/customer/followRecord/apis'
import { queryById, deleteCustomer } from '../apis'
import EditFollowRecord from '@/views/customer/followRecord/components/EditFollowRecord.vue'
import EditCustomer from './EditCustomer.vue'

const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const emits = defineEmits(['refresh'])

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getFollowRecordList)

const visible = ref(false)
const loading = ref(false)
const editFollowRecordModalRef = ref()
const editDrawerRef = ref()
const detailData = ref({})
const busiLicenceFileName = ref('')

const searchParams = reactive({
  followCustomer: undefined
})

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return dataList.findIndex((i) => i.id === detailData.value.id)
})

const tabList = [
  { title: '基础信息', name: 'basic' },
  { title: '财务信息', name: 'financial' },
  { title: '跟进记录', name: 'followRecord' }
]

const followRecordColumns = [
  { title: '跟进时间', dataIndex: 'followTime', width: 200, fixed: 'left' },
  { title: '跟进人', dataIndex: 'followPerson_dictText', width: 120 },
  { title: '跟进方式', dataIndex: 'followMethod_dictText', width: 120 },
  { title: '跟进内容', dataIndex: 'followContent', width: 160, ellipsis: true }
]

/**
 * 打开详情抽屉
 */
const open = (record) => {
  if (!record || !record.id) {
    message.error('缺少必要参数')
    return
  }

  visible.value = true
  loadDetail(record.id)
  searchParams.followCustomer = record.id
  onTableChange()
}

/**
 * 关闭抽屉
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
}

/**
 * 切换详情数据
 */
const handleSwitchDetail = (index) => {
  loadDetail(dataList[index].id)
}

/**
 * 编辑客户信息
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 刷新详情数据
 */
const refreshData = () => {
  loadDetail(detailData.value.id)
  emits('refresh')
}

/**
 * 添加跟进记录
 */
const handleAddFollowRecord = () => {
  editFollowRecordModalRef.value.open({ followCustomer: detailData.value.id })
}

/**
 * 获取客户详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const res = await queryById({ id })
    detailData.value = res.result || {}
    getBusiLicenceFileName(detailData.value.busiLicence)
  } finally {
    loading.value = false
  }
}

/**
 * 获取营业执照文件名
 */
const getBusiLicenceFileName = async (fileId) => {
  const attachmentData = await getAttachmentByIds(fileId)
  if (attachmentData && attachmentData.result && attachmentData.result.length > 0) {
    const fileInfo = attachmentData.result[0]
    busiLicenceFileName.value = `${fileInfo.fileName}.${fileInfo.fileType}`
  }
}

/**
 * 跟进记录表格变化处理
 */
const onTableChange = ({ pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo, pageSize, ...searchParams })
}

/**
 * 删除客户
 */
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除客户"${detailData.value.name || detailData.value.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteCustomer({ id: detailData.value.id })
      message.success('删除成功')
      visible.value = false
      emits('refresh')
    }
  })
}

defineExpose({
  open
})
</script>
